{"$schema": "./node_modules/oxlint/configuration_schema.json", "plugins": ["typescript"], "categories": {"correctness": "off"}, "env": {"builtin": true}, "rules": {"@typescript-eslint/adjacent-overload-signatures": "error", "@typescript-eslint/no-empty-interface": "error", "@typescript-eslint/no-inferrable-types": ["error", {"ignoreParameters": true}], "arrow-body-style": "off", "curly": "error", "eqeqeq": ["error", "always"], "max-lines": ["error", 750], "no-debugger": "error", "no-empty": "off", "no-empty-function": "off", "no-irregular-whitespace": "off", "no-redeclare": "error", "no-throw-literal": "error", "no-unused-expressions": "error", "no-var": "error", "no-unused-vars": ["warn", {"args": "all", "argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}]}}