{"name": "fastify-multer", "description": "Fastify plugin for handling `multipart/form-data`.", "version": "2.0.3", "main": "lib/index.js", "types": "lib/index.d.ts", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>> (http://www.hacksparrow.com)", "<PERSON><PERSON> <https://github.com/jpfluger>", "<PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "repository": "https://github.com/fox1t/fastify-multer", "keywords": ["form", "post", "multipart", "form-data", "formdata", "fastify", "middleware"], "dependencies": {"@fastify/busboy": "^3.1.1", "append-field": "^2.0.0", "cloudinary": "^2.7.0", "concat-stream": "^2.0.0", "fastify-plugin": "^5.0.1", "mkdirp": "^3.0.1", "on-finished": "^2.4.1", "type-is": "~2.0.1", "xtend": "^4.0.2"}, "devDependencies": {"@biomejs/biome": "^2.1.2", "@stylistic/eslint-plugin": "^5.2.2", "@types/concat-stream": "^2.0.3", "@types/mocha": "~10.0.10", "@types/node": "^24.1.0", "@types/on-finished": "^2.3.5", "@types/type-is": "^1.6.7", "@types/xtend": "4.0.6", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.31.0", "fastify": "^5.4.0", "form-data": "^4.0.4", "fs-temp": "^2.0.1", "mocha": "^11.7.1", "oxlint": "^1.8.0", "prettier": "~3.6.2", "rimraf": "^6.0.1", "testdata-w3c-json-form": "~1.0.0", "ts-node": "^10.9.2", "tslint-config-prettier": "^1.18.0", "typescript": "^5.8.3"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "files": ["lib/", "src", "typings/"], "scripts": {"prebuild": "npm run clean-build && npm run lint", "build": "tsc -p ./tsconfig.json", "clean-build": "rimraf ./lib && mkdir lib", "format": "npx --yes @biomejs/biome@latest format --write ./src", "prelint": "npm run format", "lint": "npx --yes ox<PERSON>@latest", "lint.fix": "npx --yes oxlint@latest && eslint --fix --ext .ts .", "update": "npx npm-check -u", "prepublishOnly": "npm run build", "test": "npm run build && mocha --require test/tshook.js ./test/*.ts"}, "lint-staged": {"**/*.{js,mjs,cjs,jsx,ts,mts,cts,tsx,vue,astro,svelte}": "npm run lint.fix"}}